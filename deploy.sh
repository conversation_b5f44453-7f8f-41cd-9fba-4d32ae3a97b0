#!/bin/bash

# CyberMap Local Deployment Script
# This script sets up and runs the CyberMap application locally

set -e

echo "🚀 CyberMap Local Deployment"
echo "=============================="

# Check if Docker is available
if command -v docker &> /dev/null; then
    echo "✅ Docker found - using containerized deployment"
    
    # Build and run with Docker Compose
    echo "📦 Building containers..."
    docker-compose build
    
    echo "🔄 Starting services..."
    docker-compose up -d
    
    echo "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Check if services are running
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ Backend is running at http://localhost:8080"
    else
        echo "❌ Backend failed to start"
        docker-compose logs backend
        exit 1
    fi
    
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Frontend is running at http://localhost:3000"
    else
        echo "❌ Frontend failed to start"
        docker-compose logs frontend
        exit 1
    fi
    
    echo ""
    echo "🎉 CyberMap is now running!"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:8080"
    echo ""
    echo "To stop the application, run: docker-compose down"
    
else
    echo "🔧 Docker not found - using local development setup"
    
    # Check dependencies
    if ! command -v cargo &> /dev/null; then
        echo "❌ Rust/Cargo not found. Please install Rust: https://rustup.rs/"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js not found. Please install Node.js: https://nodejs.org/"
        exit 1
    fi
    
    # Setup backend
    echo "🦀 Setting up Rust backend..."
    cd backend
    cargo build --release
    
    # Run database migrations
    echo "🗄️  Setting up database..."
    mkdir -p data
    
    # Start backend in background
    echo "🚀 Starting backend..."
    RUST_LOG=info ./target/release/cybermap-backend &
    BACKEND_PID=$!
    
    # Setup frontend
    echo "⚛️  Setting up React frontend..."
    cd ../frontend
    npm install
    npm run build
    
    # Serve frontend
    echo "🌐 Starting frontend..."
    npx serve -s dist -l 3000 &
    FRONTEND_PID=$!
    
    # Wait for services
    echo "⏳ Waiting for services to start..."
    sleep 5
    
    # Check services
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ Backend is running at http://localhost:8080"
    else
        echo "❌ Backend failed to start"
        kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
    
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Frontend is running at http://localhost:3000"
    else
        echo "❌ Frontend failed to start"
        kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
    
    echo ""
    echo "🎉 CyberMap is now running!"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:8080"
    echo ""
    echo "Backend PID: $BACKEND_PID"
    echo "Frontend PID: $FRONTEND_PID"
    echo ""
    echo "To stop the application:"
    echo "  kill $BACKEND_PID $FRONTEND_PID"
    
    # Keep script running
    wait
fi
