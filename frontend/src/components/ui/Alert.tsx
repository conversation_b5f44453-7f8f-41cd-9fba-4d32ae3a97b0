import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon, 
  XCircleIcon,
  XMarkIcon 
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

type AlertType = 'success' | 'warning' | 'error' | 'info';

interface AlertProps {
  type: AlertType;
  title?: string;
  message: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

const alertStyles = {
  success: {
    container: 'bg-green-50 border-green-200 text-green-800',
    icon: 'text-green-400',
    IconComponent: CheckCircleIcon,
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    icon: 'text-yellow-400',
    IconComponent: ExclamationTriangleIcon,
  },
  error: {
    container: 'bg-red-50 border-red-200 text-red-800',
    icon: 'text-red-400',
    IconComponent: XCircleIcon,
  },
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800',
    icon: 'text-blue-400',
    IconComponent: InformationCircleIcon,
  },
};

export function Alert({ 
  type, 
  title, 
  message, 
  dismissible = false, 
  onDismiss, 
  className 
}: AlertProps) {
  const styles = alertStyles[type];
  const { IconComponent } = styles;

  return (
    <div className={cn(
      'rounded-lg border p-4',
      styles.container,
      className
    )}>
      <div className="flex">
        <div className="flex-shrink-0">
          <IconComponent className={cn('h-5 w-5', styles.icon)} />
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className="text-sm font-medium mb-1">
              {title}
            </h3>
          )}
          <p className="text-sm">
            {message}
          </p>
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className={cn(
                  'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                  type === 'success' && 'text-green-500 hover:bg-green-100 focus:ring-green-600',
                  type === 'warning' && 'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600',
                  type === 'error' && 'text-red-500 hover:bg-red-100 focus:ring-red-600',
                  type === 'info' && 'text-blue-500 hover:bg-blue-100 focus:ring-blue-600'
                )}
              >
                <span className="sr-only">Dismiss</span>
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Convenience components for specific alert types
export function SuccessAlert(props: Omit<AlertProps, 'type'>) {
  return <Alert {...props} type="success" />;
}

export function WarningAlert(props: Omit<AlertProps, 'type'>) {
  return <Alert {...props} type="warning" />;
}

export function ErrorAlert(props: Omit<AlertProps, 'type'>) {
  return <Alert {...props} type="error" />;
}

export function InfoAlert(props: Omit<AlertProps, 'type'>) {
  return <Alert {...props} type="info" />;
}
