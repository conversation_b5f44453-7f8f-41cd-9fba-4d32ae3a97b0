import { useState, useEffect } from 'react';
import type { Asset, CreateAssetRequest, UpdateAssetRequest } from '../types';
import { LoadingButton } from './ui/LoadingSpinner';
import { ErrorAlert } from './ui/Alert';

interface AssetFormProps {
  asset?: Asset;
  onSubmit: (data: CreateAssetRequest | UpdateAssetRequest) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

const assetTypes = [
  { value: 'server', label: 'Server' },
  { value: 'workstation', label: 'Workstation' },
  { value: 'network_device', label: 'Network Device' },
  { value: 'service', label: 'Service' },
  { value: 'database', label: 'Database' },
  { value: 'application', label: 'Application' },
];

const criticalityLevels = [
  { value: 'critical', label: 'Critical' },
  { value: 'high', label: 'High' },
  { value: 'medium', label: 'Medium' },
  { value: 'low', label: 'Low' },
];

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'decommissioned', label: 'Decommissioned' },
];

export function AssetForm({ asset, onSubmit, onCancel, loading = false, error }: AssetFormProps) {
  const [formData, setFormData] = useState<CreateAssetRequest>({
    name: '',
    asset_type: 'server',
    ip_address: '',
    mac_address: '',
    hostname: '',
    operating_system: '',
    version: '',
    location: '',
    owner: '',
    criticality: 'medium',
    status: 'active',
    tags: [],
    metadata: {},
  });

  const [tagsInput, setTagsInput] = useState('');

  // Initialize form with asset data if editing
  useEffect(() => {
    if (asset) {
      setFormData({
        name: asset.name,
        asset_type: asset.asset_type,
        ip_address: asset.ip_address || '',
        mac_address: asset.mac_address || '',
        hostname: asset.hostname || '',
        operating_system: asset.operating_system || '',
        version: asset.version || '',
        location: asset.location || '',
        owner: asset.owner || '',
        criticality: asset.criticality,
        status: asset.status,
        tags: asset.tags || [],
        metadata: asset.metadata || {},
      });
      setTagsInput((asset.tags || []).join(', '));
    }
  }, [asset]);

  const handleInputChange = (field: keyof CreateAssetRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleTagsChange = (value: string) => {
    setTagsInput(value);
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    handleInputChange('tags', tags);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <ErrorAlert message={error} dismissible />
      )}

      {/* Basic Information */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Asset Name *
          </label>
          <input
            type="text"
            id="name"
            required
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="Enter asset name"
          />
        </div>

        <div>
          <label htmlFor="asset_type" className="block text-sm font-medium text-gray-700">
            Asset Type *
          </label>
          <select
            id="asset_type"
            required
            value={formData.asset_type}
            onChange={(e) => handleInputChange('asset_type', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
          >
            {assetTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Network Information */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label htmlFor="ip_address" className="block text-sm font-medium text-gray-700">
            IP Address
          </label>
          <input
            type="text"
            id="ip_address"
            value={formData.ip_address}
            onChange={(e) => handleInputChange('ip_address', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="*************"
          />
        </div>

        <div>
          <label htmlFor="mac_address" className="block text-sm font-medium text-gray-700">
            MAC Address
          </label>
          <input
            type="text"
            id="mac_address"
            value={formData.mac_address}
            onChange={(e) => handleInputChange('mac_address', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="00:11:22:33:44:55"
          />
        </div>
      </div>

      <div>
        <label htmlFor="hostname" className="block text-sm font-medium text-gray-700">
          Hostname
        </label>
        <input
          type="text"
          id="hostname"
          value={formData.hostname}
          onChange={(e) => handleInputChange('hostname', e.target.value)}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
          placeholder="server01.example.com"
        />
      </div>

      {/* System Information */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label htmlFor="operating_system" className="block text-sm font-medium text-gray-700">
            Operating System
          </label>
          <input
            type="text"
            id="operating_system"
            value={formData.operating_system}
            onChange={(e) => handleInputChange('operating_system', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="Ubuntu 22.04, Windows Server 2022, etc."
          />
        </div>

        <div>
          <label htmlFor="version" className="block text-sm font-medium text-gray-700">
            Version
          </label>
          <input
            type="text"
            id="version"
            value={formData.version}
            onChange={(e) => handleInputChange('version', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="Version number or build"
          />
        </div>
      </div>

      {/* Management Information */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700">
            Location
          </label>
          <input
            type="text"
            id="location"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="Data Center A, Office Building, etc."
          />
        </div>

        <div>
          <label htmlFor="owner" className="block text-sm font-medium text-gray-700">
            Owner
          </label>
          <input
            type="text"
            id="owner"
            value={formData.owner}
            onChange={(e) => handleInputChange('owner', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            placeholder="Team or person responsible"
          />
        </div>
      </div>

      {/* Classification */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label htmlFor="criticality" className="block text-sm font-medium text-gray-700">
            Criticality *
          </label>
          <select
            id="criticality"
            required
            value={formData.criticality}
            onChange={(e) => handleInputChange('criticality', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
          >
            {criticalityLevels.map(level => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
            Status *
          </label>
          <select
            id="status"
            required
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
          >
            {statusOptions.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Tags */}
      <div>
        <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
          Tags
        </label>
        <input
          type="text"
          id="tags"
          value={tagsInput}
          onChange={(e) => handleTagsChange(e.target.value)}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
          placeholder="web-server, production, critical (comma-separated)"
        />
        <p className="mt-1 text-xs text-gray-500">
          Enter tags separated by commas
        </p>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-secondary"
          disabled={loading}
        >
          Cancel
        </button>
        <LoadingButton
          type="submit"
          loading={loading}
          className="btn-primary"
        >
          {asset ? 'Update Asset' : 'Create Asset'}
        </LoadingButton>
      </div>
    </form>
  );
}
