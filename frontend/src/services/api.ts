import type {
  Asset,
  CreateAssetRequest,
  UpdateAssetRequest,
  Assessment,
  CreateAssessmentRequest,
  Framework,
  Configuration,
  NetworkTopology,
  ImportResult,
  DashboardStats,
  RecentActivity,

  AssetFilters,
  AssessmentFilters,
} from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

class ApiError extends Error {
  public status: number;

  constructor(status: number, message: string) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new ApiError(response.status, `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(0, `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Asset API
export const assetApi = {
  // Get all assets with optional filters
  getAssets: async (filters?: AssetFilters): Promise<Asset[]> => {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.asset_type) params.append('asset_type', filters.asset_type);
    if (filters?.criticality) params.append('criticality', filters.criticality);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.location) params.append('location', filters.location);
    if (filters?.owner) params.append('owner', filters.owner);
    
    const query = params.toString();
    return apiRequest<Asset[]>(`/assets${query ? `?${query}` : ''}`);
  },

  // Get single asset by ID
  getAsset: async (id: string): Promise<Asset> => {
    return apiRequest<Asset>(`/assets/${id}`);
  },

  // Create new asset
  createAsset: async (asset: CreateAssetRequest): Promise<Asset> => {
    return apiRequest<Asset>('/assets', {
      method: 'POST',
      body: JSON.stringify(asset),
    });
  },

  // Update existing asset
  updateAsset: async (id: string, updates: UpdateAssetRequest): Promise<Asset> => {
    return apiRequest<Asset>(`/assets/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // Delete asset
  deleteAsset: async (id: string): Promise<void> => {
    return apiRequest<void>(`/assets/${id}`, {
      method: 'DELETE',
    });
  },

  // Import assets from file
  importAssets: async (file: File, type: 'nmap' | 'csv'): Promise<ImportResult> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return apiRequest<ImportResult>('/assets/import', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  },
};

// Assessment API
export const assessmentApi = {
  // Get all assessments with optional filters
  getAssessments: async (filters?: AssessmentFilters): Promise<Assessment[]> => {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.framework_id) params.append('framework_id', filters.framework_id);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.score_min !== undefined) params.append('score_min', filters.score_min.toString());
    if (filters?.score_max !== undefined) params.append('score_max', filters.score_max.toString());
    
    const query = params.toString();
    return apiRequest<Assessment[]>(`/assessments${query ? `?${query}` : ''}`);
  },

  // Get single assessment by ID
  getAssessment: async (id: string): Promise<Assessment> => {
    return apiRequest<Assessment>(`/assessments/${id}`);
  },

  // Create new assessment
  createAssessment: async (assessment: CreateAssessmentRequest): Promise<Assessment> => {
    return apiRequest<Assessment>('/assessments', {
      method: 'POST',
      body: JSON.stringify(assessment),
    });
  },

  // Update assessment
  updateAssessment: async (id: string, updates: Partial<Assessment>): Promise<Assessment> => {
    return apiRequest<Assessment>(`/assessments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // Delete assessment
  deleteAssessment: async (id: string): Promise<void> => {
    return apiRequest<void>(`/assessments/${id}`, {
      method: 'DELETE',
    });
  },
};

// Framework API
export const frameworkApi = {
  // Get all frameworks
  getFrameworks: async (): Promise<Framework[]> => {
    return apiRequest<Framework[]>('/frameworks');
  },

  // Get single framework by ID
  getFramework: async (id: string): Promise<Framework> => {
    return apiRequest<Framework>(`/frameworks/${id}`);
  },
};

// Configuration API
export const configurationApi = {
  // Get configurations for an asset
  getAssetConfigurations: async (assetId: string): Promise<Configuration[]> => {
    return apiRequest<Configuration[]>(`/assets/${assetId}/configurations`);
  },

  // Create configuration for an asset
  createConfiguration: async (assetId: string, config: Omit<Configuration, 'id' | 'asset_id' | 'created_at' | 'updated_at'>): Promise<Configuration> => {
    return apiRequest<Configuration>(`/assets/${assetId}/configurations`, {
      method: 'POST',
      body: JSON.stringify(config),
    });
  },

  // Update configuration
  updateConfiguration: async (id: string, updates: Partial<Configuration>): Promise<Configuration> => {
    return apiRequest<Configuration>(`/configurations/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // Delete configuration
  deleteConfiguration: async (id: string): Promise<void> => {
    return apiRequest<void>(`/configurations/${id}`, {
      method: 'DELETE',
    });
  },
};

// Network Topology API
export const networkApi = {
  // Get network topology
  getTopology: async (): Promise<NetworkTopology> => {
    return apiRequest<NetworkTopology>('/network/topology');
  },
};

// Dashboard API
export const dashboardApi = {
  // Get dashboard statistics
  getStats: async (): Promise<DashboardStats> => {
    return apiRequest<DashboardStats>('/dashboard/stats');
  },

  // Get recent activity
  getRecentActivity: async (limit: number = 10): Promise<RecentActivity[]> => {
    return apiRequest<RecentActivity[]>(`/dashboard/activity?limit=${limit}`);
  },
};

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; version: string }> => {
    const url = `${API_BASE_URL.replace('/api', '')}/health`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new ApiError(response.status, `HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  },
};

export { ApiError };
