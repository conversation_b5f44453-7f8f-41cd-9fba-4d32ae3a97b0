import { DocumentTextIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

export function Reports() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
        <p className="mt-1 text-sm text-gray-500">
          Generate and export cybersecurity reports and documentation
        </p>
      </div>

      {/* Report Templates */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Report Templates</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div className="border border-gray-200 rounded-lg p-4">
            <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">Asset Inventory Report</h3>
            <p className="text-xs text-gray-500 mb-3">Complete listing of all infrastructure assets</p>
            <button className="btn-primary text-xs w-full">Generate</button>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">NIST CSF Assessment</h3>
            <p className="text-xs text-gray-500 mb-3">Framework compliance and gap analysis</p>
            <button className="btn-secondary text-xs w-full" disabled>No Data</button>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">CIS Controls Report</h3>
            <p className="text-xs text-gray-500 mb-3">Implementation status and recommendations</p>
            <button className="btn-secondary text-xs w-full" disabled>No Data</button>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">Network Topology</h3>
            <p className="text-xs text-gray-500 mb-3">Infrastructure diagram and relationships</p>
            <button className="btn-secondary text-xs w-full" disabled>No Data</button>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">Executive Summary</h3>
            <p className="text-xs text-gray-500 mb-3">High-level security posture overview</p>
            <button className="btn-secondary text-xs w-full" disabled>No Data</button>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">Custom Report</h3>
            <p className="text-xs text-gray-500 mb-3">Build your own report template</p>
            <button className="btn-primary text-xs w-full">Create</button>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Export Options</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="text-center p-4 border border-gray-200 rounded-lg">
            <ArrowDownTrayIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <h3 className="text-sm font-medium text-gray-900">PDF Report</h3>
            <p className="text-xs text-gray-500">Professional formatted document</p>
          </div>
          
          <div className="text-center p-4 border border-gray-200 rounded-lg">
            <ArrowDownTrayIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <h3 className="text-sm font-medium text-gray-900">CSV Data</h3>
            <p className="text-xs text-gray-500">Raw data for analysis</p>
          </div>
          
          <div className="text-center p-4 border border-gray-200 rounded-lg">
            <ArrowDownTrayIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <h3 className="text-sm font-medium text-gray-900">JSON Export</h3>
            <p className="text-xs text-gray-500">Machine-readable format</p>
          </div>
        </div>
      </div>

      {/* Recent Reports */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Reports</h2>
        <div className="text-center py-8">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No reports generated</h3>
          <p className="mt-1 text-sm text-gray-500">
            Generated reports will appear here for easy access
          </p>
        </div>
      </div>
    </div>
  );
}
