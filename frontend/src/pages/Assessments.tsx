import { PlusIcon, ClipboardDocumentCheckIcon } from '@heroicons/react/24/outline';

const frameworks = [
  {
    id: 'nist-csf',
    name: 'NIST Cybersecurity Framework',
    version: '2.0',
    description: 'Comprehensive framework for managing cybersecurity risk',
    functions: ['Identify', 'Protect', 'Detect', 'Respond', 'Recover'],
  },
  {
    id: 'cis-controls',
    name: 'CIS Controls',
    version: '8.0',
    description: 'Prioritized set of actions for cyber defense',
    controls: 18,
  },
];

export function Assessments() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Security Assessments</h1>
          <p className="mt-1 text-sm text-gray-500">
            Evaluate your cybersecurity posture against industry frameworks
          </p>
        </div>
        <button className="btn-primary flex items-center">
          <PlusIcon className="h-4 w-4 mr-2" />
          New Assessment
        </button>
      </div>

      {/* Current Assessments */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Current Assessments</h2>
        <div className="text-center py-8">
          <ClipboardDocumentCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No assessments</h3>
          <p className="mt-1 text-sm text-gray-500">
            Start your first security assessment to evaluate your posture
          </p>
          <div className="mt-6">
            <button className="btn-primary">
              <PlusIcon className="h-4 w-4 mr-2" />
              Start Assessment
            </button>
          </div>
        </div>
      </div>

      {/* Available Frameworks */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Available Frameworks</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {frameworks.map((framework) => (
            <div key={framework.id} className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">{framework.name}</h3>
                  <p className="text-xs text-gray-500 mb-2">Version {framework.version}</p>
                  <p className="text-sm text-gray-600 mb-3">{framework.description}</p>
                  
                  {framework.functions && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {framework.functions.map((func) => (
                        <span key={func} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          {func}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  {framework.controls && (
                    <p className="text-xs text-gray-500 mb-3">{framework.controls} Controls</p>
                  )}
                </div>
              </div>
              
              <div className="flex gap-2">
                <button className="btn-primary text-xs">Start Assessment</button>
                <button className="btn-secondary text-xs">View Details</button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Assessment History */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Assessment History</h2>
        <div className="text-center py-8">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No completed assessments</h3>
          <p className="mt-1 text-sm text-gray-500">
            Completed assessments will appear here for tracking progress over time
          </p>
        </div>
      </div>
    </div>
  );
}
