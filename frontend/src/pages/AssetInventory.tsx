import { useState } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  ServerIcon,
  ComputerDesktopIcon,
  CpuChipIcon,
  CloudIcon,
  CircleStackIcon,
  CommandLineIcon
} from '@heroicons/react/24/outline';
import { useAssets } from '../hooks/useAssets';
import type { Asset, AssetFilters } from '../types';
import { Modal } from '../components/ui/Modal';
import { AssetForm } from '../components/AssetForm';
import { LoadingOverlay } from '../components/ui/LoadingSpinner';
import { ErrorAlert, SuccessAlert } from '../components/ui/Alert';
import { cn } from '../utils/cn';

const assetTypeIcons = {
  server: ServerIcon,
  workstation: ComputerDesktopIcon,
  network_device: CpuChipIcon,
  service: CloudIcon,
  database: CircleStackIcon,
  application: CommandLineIcon,
};

const criticalityColors = {
  critical: 'bg-red-100 text-red-800',
  high: 'bg-orange-100 text-orange-800',
  medium: 'bg-yellow-100 text-yellow-800',
  low: 'bg-green-100 text-green-800',
};

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  decommissioned: 'bg-red-100 text-red-800',
};

export function AssetInventory() {
  const [filters, setFilters] = useState<AssetFilters>({});
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const {
    assets,
    loading,
    error,
    createAsset,
    updateAsset,
    deleteAsset,
    fetchAssets,
    clearError,
  } = useAssets(filters);

  const handleFilterChange = (key: keyof AssetFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    fetchAssets(newFilters);
  };

  const handleCreateAsset = async (data: any) => {
    const result = await createAsset(data);
    if (result) {
      setIsCreateModalOpen(false);
      setSuccessMessage('Asset created successfully!');
      setTimeout(() => setSuccessMessage(null), 5000);
    }
  };

  const handleEditAsset = async (data: any) => {
    if (!selectedAsset) return;
    const result = await updateAsset(selectedAsset.id, data);
    if (result) {
      setIsEditModalOpen(false);
      setSelectedAsset(null);
      setSuccessMessage('Asset updated successfully!');
      setTimeout(() => setSuccessMessage(null), 5000);
    }
  };

  const handleDeleteAsset = async (asset: Asset) => {
    if (window.confirm(`Are you sure you want to delete "${asset.name}"?`)) {
      const success = await deleteAsset(asset.id);
      if (success) {
        setSuccessMessage('Asset deleted successfully!');
        setTimeout(() => setSuccessMessage(null), 5000);
      }
    }
  };

  const openEditModal = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsEditModalOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {successMessage && (
        <SuccessAlert message={successMessage} dismissible onDismiss={() => setSuccessMessage(null)} />
      )}

      {/* Error Message */}
      {error && (
        <ErrorAlert message={error} dismissible onDismiss={clearError} />
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Asset Inventory</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage and track all infrastructure assets ({assets.length} total)
          </p>
        </div>
        <button
          className="btn-primary flex items-center"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Asset
        </button>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Search assets..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
                className="pl-10 w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-primary-500 focus:ring-primary-500"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={filters.asset_type || ''}
              onChange={(e) => handleFilterChange('asset_type', e.target.value || undefined)}
              className="rounded-lg border border-gray-300 px-3 py-2 focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">All Types</option>
              <option value="server">Server</option>
              <option value="workstation">Workstation</option>
              <option value="network_device">Network Device</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="application">Application</option>
            </select>
            <select
              value={filters.criticality || ''}
              onChange={(e) => handleFilterChange('criticality', e.target.value || undefined)}
              className="rounded-lg border border-gray-300 px-3 py-2 focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">All Criticality</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
              className="rounded-lg border border-gray-300 px-3 py-2 focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="decommissioned">Decommissioned</option>
            </select>
          </div>
        </div>
      </div>

      {/* Asset List */}
      <div className="card">
        {loading ? (
          <LoadingOverlay message="Loading assets..." />
        ) : assets.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No assets found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {Object.keys(filters).length > 0
                ? 'Try adjusting your search filters or add your first asset.'
                : 'Get started by adding your first asset.'
              }
            </p>
            <div className="mt-6">
              <button
                className="btn-primary"
                onClick={() => setIsCreateModalOpen(true)}
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Asset
              </button>
            </div>
          </div>
        ) : (
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Asset
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Network
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Criticality
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Owner
                  </th>
                  <th className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {assets.map((asset) => {
                  const IconComponent = assetTypeIcons[asset.asset_type] || ServerIcon;
                  return (
                    <tr key={asset.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-lg bg-gray-100 flex items-center justify-center">
                              <IconComponent className="h-5 w-5 text-gray-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{asset.name}</div>
                            {asset.hostname && (
                              <div className="text-sm text-gray-500">{asset.hostname}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900 capitalize">
                          {asset.asset_type.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{asset.ip_address || '-'}</div>
                        {asset.mac_address && (
                          <div className="text-sm text-gray-500">{asset.mac_address}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={cn(
                          'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                          criticalityColors[asset.criticality]
                        )}>
                          {asset.criticality}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={cn(
                          'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                          statusColors[asset.status]
                        )}>
                          {asset.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {asset.owner || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => openEditModal(asset)}
                            className="text-primary-600 hover:text-primary-900 p-1 rounded"
                            title="Edit asset"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteAsset(asset)}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                            title="Delete asset"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Import Options */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Import Assets</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <button className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors">
            <div className="text-center">
              <div className="mx-auto h-8 w-8 text-gray-400 mb-2">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-sm font-medium text-gray-900">Nmap XML</h3>
              <p className="text-xs text-gray-500">Import network scan results</p>
            </div>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors">
            <div className="text-center">
              <div className="mx-auto h-8 w-8 text-gray-400 mb-2">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-sm font-medium text-gray-900">CSV File</h3>
              <p className="text-xs text-gray-500">Import from spreadsheet</p>
            </div>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors">
            <div className="text-center">
              <div className="mx-auto h-8 w-8 text-gray-400 mb-2">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                </svg>
              </div>
              <h3 className="text-sm font-medium text-gray-900">Cloud API</h3>
              <p className="text-xs text-gray-500">Connect to AWS/Azure/GCP</p>
            </div>
          </button>
        </div>
      </div>

      {/* Create Asset Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Asset"
        size="lg"
      >
        <AssetForm
          onSubmit={handleCreateAsset}
          onCancel={() => setIsCreateModalOpen(false)}
          loading={loading}
          error={error}
        />
      </Modal>

      {/* Edit Asset Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedAsset(null);
        }}
        title="Edit Asset"
        size="lg"
      >
        <AssetForm
          asset={selectedAsset || undefined}
          onSubmit={handleEditAsset}
          onCancel={() => {
            setIsEditModalOpen(false);
            setSelectedAsset(null);
          }}
          loading={loading}
          error={error}
        />
      </Modal>
    </div>
  );
}
