import {
  ServerIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { useDashboard } from '../hooks/useDashboard';
import { LoadingOverlay } from '../components/ui/LoadingSpinner';
import { ErrorAlert } from '../components/ui/Alert';
import { cn } from '../utils/cn';



export function Dashboard() {
  const { stats, recentActivity, loading, error, refreshDashboard, clearError } = useDashboard();

  const getStatsDisplay = () => {
    if (!stats) {
      return [
        {
          name: 'Total Assets',
          value: '0',
          icon: ServerIcon,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
        },
        {
          name: 'Security Score',
          value: '0%',
          icon: ShieldCheckIcon,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
        },
        {
          name: 'Critical Issues',
          value: '0',
          icon: ExclamationTriangleIcon,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
        },
        {
          name: 'Assessments',
          value: '0',
          icon: ChartBarIcon,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
        },
      ];
    }

    return [
      {
        name: 'Total Assets',
        value: stats.total_assets.toString(),
        icon: ServerIcon,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
      },
      {
        name: 'Security Score',
        value: `${Math.round(stats.average_security_score)}%`,
        icon: ShieldCheckIcon,
        color: stats.average_security_score >= 80 ? 'text-green-600' :
               stats.average_security_score >= 60 ? 'text-yellow-600' : 'text-red-600',
        bgColor: stats.average_security_score >= 80 ? 'bg-green-100' :
                 stats.average_security_score >= 60 ? 'bg-yellow-100' : 'bg-red-100',
      },
      {
        name: 'Critical Issues',
        value: stats.critical_issues.toString(),
        icon: ExclamationTriangleIcon,
        color: 'text-red-600',
        bgColor: 'bg-red-100',
      },
      {
        name: 'Assessments',
        value: stats.total_assessments.toString(),
        icon: ChartBarIcon,
        color: 'text-purple-600',
        bgColor: 'bg-purple-100',
      },
    ];
  };

  const statsDisplay = getStatsDisplay();

  const defaultActivity = [
    {
      id: '1',
      activity_type: 'info' as const,
      message: 'Welcome to CyberMap! Start by adding your first asset.',
      timestamp: new Date().toISOString(),
      user: 'System',
    },
  ];

  const activityToShow = recentActivity.length > 0 ? recentActivity : defaultActivity;
  if (loading) {
    return <LoadingOverlay message="Loading dashboard..." />;
  }

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <ErrorAlert message={error} dismissible onDismiss={clearError} />
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Overview of your cybersecurity infrastructure and posture
          </p>
        </div>
        <button
          onClick={refreshDashboard}
          className="btn-secondary"
          disabled={loading}
        >
          Refresh
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statsDisplay.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <button className="btn-primary text-left p-4 rounded-lg border border-gray-200 hover:border-primary-300 transition-colors">
            <div className="flex items-center">
              <ServerIcon className="h-6 w-6 text-primary-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">Add Assets</h3>
                <p className="text-xs text-gray-500">Import or manually add infrastructure assets</p>
              </div>
            </div>
          </button>
          
          <button className="btn-secondary text-left p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
            <div className="flex items-center">
              <ChartBarIcon className="h-6 w-6 text-gray-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">Start Assessment</h3>
                <p className="text-xs text-gray-500">Begin NIST CSF or CIS Controls assessment</p>
              </div>
            </div>
          </button>
          
          <button className="btn-secondary text-left p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
            <div className="flex items-center">
              <ShieldCheckIcon className="h-6 w-6 text-gray-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">View Network Map</h3>
                <p className="text-xs text-gray-500">Visualize your infrastructure topology</p>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-3">
          {activityToShow.map((activity) => {
            const activityTypeColors = {
              info: 'bg-blue-400',
              success: 'bg-green-400',
              warning: 'bg-yellow-400',
              error: 'bg-red-400',
            };

            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className={cn(
                    'h-2 w-2 rounded-full mt-2',
                    activityTypeColors[activity.activity_type]
                  )}></div>
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">
                    {activity.user && `${activity.user} • `}
                    {new Date(activity.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Framework Status */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 mb-4">NIST CSF Status</h2>
          <div className="space-y-3">
            {['Identify', 'Protect', 'Detect', 'Respond', 'Recover'].map((func) => (
              <div key={func} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{func}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-gray-300 h-2 rounded-full" style={{ width: '0%' }}></div>
                  </div>
                  <span className="text-xs text-gray-500">0%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 mb-4">CIS Controls Status</h2>
          <div className="space-y-3">
            <div className="text-center py-8">
              <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No assessments completed yet</p>
              <button className="btn-primary mt-2">Start Assessment</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
