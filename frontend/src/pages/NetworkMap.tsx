import { MapIcon } from '@heroicons/react/24/outline';

export function NetworkMap() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Network Map</h1>
        <p className="mt-1 text-sm text-gray-500">
          Interactive visualization of your infrastructure topology
        </p>
      </div>

      {/* Map Container */}
      <div className="card h-96">
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <MapIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No network topology</h3>
            <p className="mt-1 text-sm text-gray-500">
              Add assets and relationships to visualize your network
            </p>
            <div className="mt-6">
              <button className="btn-primary">Add Assets</button>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Map Controls</h2>
        <div className="flex flex-wrap gap-2">
          <button className="btn-secondary">Zoom In</button>
          <button className="btn-secondary">Zoom Out</button>
          <button className="btn-secondary">Reset View</button>
          <button className="btn-secondary">Export Image</button>
        </div>
      </div>
    </div>
  );
}
