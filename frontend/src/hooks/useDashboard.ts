import { useState, useEffect, useCallback } from 'react';
import type { DashboardStats, RecentActivity } from '../types';
import { dashboardApi, ApiError } from '../services/api';

interface UseDashboardState {
  stats: DashboardStats | null;
  recentActivity: RecentActivity[];
  loading: boolean;
  error: string | null;
}

interface UseDashboardActions {
  fetchStats: () => Promise<void>;
  fetchRecentActivity: (limit?: number) => Promise<void>;
  refreshDashboard: () => Promise<void>;
  clearError: () => void;
}

export function useDashboard(): UseDashboardState & UseDashboardActions {
  const [state, setState] = useState<UseDashboardState>({
    stats: null,
    recentActivity: [],
    loading: false,
    error: null,
  });

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const setStats = (stats: DashboardStats | null) => {
    setState(prev => ({ ...prev, stats }));
  };

  const setRecentActivity = (recentActivity: RecentActivity[]) => {
    setState(prev => ({ ...prev, recentActivity }));
  };

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const stats = await dashboardApi.getStats();
      setStats(stats);
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to fetch dashboard stats: ${error.message}`
        : 'An unexpected error occurred while fetching dashboard statistics';
      setError(errorMessage);
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchRecentActivity = useCallback(async (limit: number = 10) => {
    setLoading(true);
    setError(null);
    
    try {
      const activity = await dashboardApi.getRecentActivity(limit);
      setRecentActivity(activity);
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to fetch recent activity: ${error.message}`
        : 'An unexpected error occurred while fetching recent activity';
      setError(errorMessage);
      console.error('Error fetching recent activity:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch both stats and recent activity in parallel
      const [stats, activity] = await Promise.all([
        dashboardApi.getStats(),
        dashboardApi.getRecentActivity(10)
      ]);
      
      setStats(stats);
      setRecentActivity(activity);
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to refresh dashboard: ${error.message}`
        : 'An unexpected error occurred while refreshing the dashboard';
      setError(errorMessage);
      console.error('Error refreshing dashboard:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Initial fetch on mount
  useEffect(() => {
    refreshDashboard();
  }, [refreshDashboard]);

  return {
    ...state,
    fetchStats,
    fetchRecentActivity,
    refreshDashboard,
    clearError,
  };
}
