import { useState, useEffect, useCallback } from 'react';
import type { Asset, CreateAssetRequest, UpdateAssetRequest, AssetFilters, ImportResult } from '../types';
import { assetApi, ApiError } from '../services/api';

interface UseAssetsState {
  assets: Asset[];
  loading: boolean;
  error: string | null;
  selectedAsset: Asset | null;
}

interface UseAssetsActions {
  fetchAssets: (filters?: AssetFilters) => Promise<void>;
  createAsset: (asset: CreateAssetRequest) => Promise<Asset | null>;
  updateAsset: (id: string, updates: UpdateAssetRequest) => Promise<Asset | null>;
  deleteAsset: (id: string) => Promise<boolean>;
  selectAsset: (asset: Asset | null) => void;
  importAssets: (file: File, type: 'nmap' | 'csv') => Promise<ImportResult | null>;
  clearError: () => void;
  refreshAssets: () => Promise<void>;
}

export function useAssets(initialFilters?: AssetFilters): UseAssetsState & UseAssetsActions {
  const [state, setState] = useState<UseAssetsState>({
    assets: [],
    loading: false,
    error: null,
    selectedAsset: null,
  });

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const setAssets = (assets: Asset[]) => {
    setState(prev => ({ ...prev, assets }));
  };

  const setSelectedAsset = (selectedAsset: Asset | null) => {
    setState(prev => ({ ...prev, selectedAsset }));
  };

  const fetchAssets = useCallback(async (filters?: AssetFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const assets = await assetApi.getAssets(filters);
      setAssets(assets);
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to fetch assets: ${error.message}`
        : 'An unexpected error occurred while fetching assets';
      setError(errorMessage);
      console.error('Error fetching assets:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const createAsset = useCallback(async (asset: CreateAssetRequest): Promise<Asset | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const newAsset = await assetApi.createAsset(asset);
      setAssets([...state.assets, newAsset]);
      return newAsset;
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to create asset: ${error.message}`
        : 'An unexpected error occurred while creating the asset';
      setError(errorMessage);
      console.error('Error creating asset:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAsset = useCallback(async (id: string, updates: UpdateAssetRequest): Promise<Asset | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedAsset = await assetApi.updateAsset(id, updates);
      setAssets(state.assets.map((asset: Asset) => asset.id === id ? updatedAsset : asset));
      
      // Update selected asset if it's the one being updated
      if (state.selectedAsset?.id === id) {
        setSelectedAsset(updatedAsset);
      }
      
      return updatedAsset;
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to update asset: ${error.message}`
        : 'An unexpected error occurred while updating the asset';
      setError(errorMessage);
      console.error('Error updating asset:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [state.selectedAsset]);

  const deleteAsset = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      await assetApi.deleteAsset(id);
      setAssets(state.assets.filter((asset: Asset) => asset.id !== id));
      
      // Clear selected asset if it's the one being deleted
      if (state.selectedAsset?.id === id) {
        setSelectedAsset(null);
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to delete asset: ${error.message}`
        : 'An unexpected error occurred while deleting the asset';
      setError(errorMessage);
      console.error('Error deleting asset:', error);
      return false;
    } finally {
      setLoading(false);
    }
  }, [state.selectedAsset]);

  const importAssets = useCallback(async (file: File, type: 'nmap' | 'csv'): Promise<ImportResult | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await assetApi.importAssets(file, type);
      
      // Refresh assets after import
      await fetchAssets(initialFilters);
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? `Failed to import assets: ${error.message}`
        : 'An unexpected error occurred while importing assets';
      setError(errorMessage);
      console.error('Error importing assets:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchAssets, initialFilters]);

  const selectAsset = useCallback((asset: Asset | null) => {
    setSelectedAsset(asset);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshAssets = useCallback(async () => {
    await fetchAssets(initialFilters);
  }, [fetchAssets, initialFilters]);

  // Initial fetch on mount
  useEffect(() => {
    fetchAssets(initialFilters);
  }, [fetchAssets, initialFilters]);

  return {
    ...state,
    fetchAssets,
    createAsset,
    updateAsset,
    deleteAsset,
    selectAsset,
    importAssets,
    clearError,
    refreshAssets,
  };
}
