// Asset Management Types
export interface Asset {
  id: string;
  name: string;
  asset_type: 'server' | 'workstation' | 'network_device' | 'service' | 'database' | 'application';
  ip_address?: string;
  mac_address?: string;
  hostname?: string;
  operating_system?: string;
  version?: string;
  location?: string;
  owner?: string;
  criticality: 'critical' | 'high' | 'medium' | 'low';
  status: 'active' | 'inactive' | 'decommissioned';
  tags?: string[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateAssetRequest {
  name: string;
  asset_type: Asset['asset_type'];
  ip_address?: string;
  mac_address?: string;
  hostname?: string;
  operating_system?: string;
  version?: string;
  location?: string;
  owner?: string;
  criticality: Asset['criticality'];
  status: Asset['status'];
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateAssetRequest extends Partial<CreateAssetRequest> {}

// Configuration Management Types
export interface Configuration {
  id: string;
  asset_id: string;
  config_type: string;
  name: string;
  description?: string;
  value: string;
  compliance_status: 'compliant' | 'non_compliant' | 'unknown';
  created_at: string;
  updated_at: string;
}

// Framework Types
export interface Framework {
  id: string;
  name: string;
  version: string;
  description?: string;
  controls: any[];
  created_at: string;
}

// Assessment Types
export interface Assessment {
  id: string;
  name: string;
  framework_id: string;
  description?: string;
  status: 'draft' | 'in_progress' | 'completed';
  score?: number;
  findings?: any[];
  recommendations?: any[];
  created_at: string;
  updated_at: string;
}

export interface CreateAssessmentRequest {
  name: string;
  framework_id: string;
  description?: string;
}

// Relationship Types
export interface AssetRelationship {
  id: string;
  source_asset_id: string;
  target_asset_id: string;
  relationship_type: 'connects_to' | 'depends_on' | 'manages' | 'hosts' | 'monitors';
  description?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface NetworkTopology {
  assets: Asset[];
  relationships: AssetRelationship[];
}

// Import Types
export interface ImportResult {
  success: boolean;
  assets_imported: number;
  assets_updated: number;
  errors: string[];
}

// NIST CSF Types
export interface NistCsfControl {
  function: 'Identify' | 'Protect' | 'Detect' | 'Respond' | 'Recover';
  category: string;
  subcategory: string;
  description: string;
  implementation_status: 'implemented' | 'partial' | 'not_implemented';
  maturity_level: 1 | 2 | 3 | 4;
}

// CIS Controls Types
export interface CisSafeguard {
  number: string;
  title: string;
  description: string;
  implementation_group: 1 | 2 | 3;
  status: 'implemented' | 'partial' | 'not_implemented';
}

export interface CisControl {
  control_number: number;
  title: string;
  description: string;
  safeguards: CisSafeguard[];
  implementation_status: 'implemented' | 'partial' | 'not_implemented';
}

// UI State Types
export interface DashboardStats {
  total_assets: number;
  total_assessments: number;
  critical_issues: number;
  average_security_score: number;
}

export interface RecentActivity {
  id: string;
  activity_type: 'info' | 'warning' | 'error' | 'success';
  message: string;
  timestamp: string;
  user: string;
  asset_id?: string;
  assessment_id?: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Filter and Search Types
export interface AssetFilters {
  search?: string;
  asset_type?: Asset['asset_type'];
  criticality?: Asset['criticality'];
  status?: Asset['status'];
  location?: string;
  owner?: string;
  tags?: string[];
}

export interface AssessmentFilters {
  search?: string;
  framework_id?: string;
  status?: Assessment['status'];
  score_min?: number;
  score_max?: number;
}
