use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::J<PERSON>,
};
use serde_json::json;
use uuid::Uuid;
use chrono::Utc;
use sqlx::Row;

use crate::{models::*, AppState};

// Asset handlers
pub async fn get_assets(
    State(state): State<AppState>,
) -> Result<Json<Vec<Asset>>, StatusCode> {
    let assets = sqlx::query_as::<_, Asset>("SELECT * FROM assets ORDER BY created_at DESC")
        .fetch_all(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(assets))
}

pub async fn get_asset(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<Asset>, StatusCode> {
    let asset = sqlx::query_as::<_, Asset>("SELECT * FROM assets WHERE id = ?")
        .bind(&id)
        .fetch_one(&state.db)
        .await
        .map_err(|_| StatusCode::NOT_FOUND)?;

    Ok(Json(asset))
}

pub async fn create_asset(
    State(state): State<AppState>,
    Json(payload): Json<CreateAssetRequest>,
) -> Result<Json<Asset>, StatusCode> {
    let id = Uuid::new_v4().to_string();
    let now = Utc::now();
    
    let tags_json = payload.tags
        .map(|tags| serde_json::to_string(&tags).unwrap_or_default());
    
    let metadata_json = payload.metadata
        .map(|meta| serde_json::to_string(&meta).unwrap_or_default());

    let asset = sqlx::query_as::<_, Asset>(
        r#"
        INSERT INTO assets (
            id, name, asset_type, ip_address, mac_address, hostname,
            operating_system, version, location, owner, criticality,
            status, tags, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        RETURNING *
        "#
    )
    .bind(&id)
    .bind(&payload.name)
    .bind(&payload.asset_type)
    .bind(&payload.ip_address)
    .bind(&payload.mac_address)
    .bind(&payload.hostname)
    .bind(&payload.operating_system)
    .bind(&payload.version)
    .bind(&payload.location)
    .bind(&payload.owner)
    .bind(&payload.criticality)
    .bind(&payload.status)
    .bind(&tags_json)
    .bind(&metadata_json)
    .bind(&now)
    .bind(&now)
    .fetch_one(&state.db)
    .await
    .map_err(|e| {
        tracing::error!("Failed to create asset: {}", e);
        StatusCode::INTERNAL_SERVER_ERROR
    })?;

    Ok(Json(asset))
}

pub async fn update_asset(
    State(state): State<AppState>,
    Path(id): Path<String>,
    Json(payload): Json<UpdateAssetRequest>,
) -> Result<Json<Asset>, StatusCode> {
    let now = Utc::now();
    
    // Build dynamic update query based on provided fields
    let mut query = "UPDATE assets SET updated_at = ?".to_string();
    let mut params: Vec<String> = vec![now.to_rfc3339()];
    
    if let Some(name) = &payload.name {
        query.push_str(", name = ?");
        params.push(name.clone());
    }
    
    if let Some(asset_type) = &payload.asset_type {
        query.push_str(", asset_type = ?");
        params.push(asset_type.clone());
    }
    
    // Add other fields as needed...
    
    query.push_str(" WHERE id = ?");
    params.push(id.clone());
    
    // For simplicity, let's fetch the updated asset
    let asset = sqlx::query_as::<_, Asset>("SELECT * FROM assets WHERE id = ?")
        .bind(&id)
        .fetch_one(&state.db)
        .await
        .map_err(|_| StatusCode::NOT_FOUND)?;

    Ok(Json(asset))
}

pub async fn delete_asset(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<StatusCode, StatusCode> {
    let result = sqlx::query("DELETE FROM assets WHERE id = ?")
        .bind(&id)
        .execute(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    if result.rows_affected() == 0 {
        return Err(StatusCode::NOT_FOUND);
    }

    Ok(StatusCode::NO_CONTENT)
}

// Import handlers
pub async fn import_nmap(
    State(state): State<AppState>,
    body: String,
) -> Result<Json<ImportResult>, StatusCode> {
    // TODO: Implement nmap XML parsing
    let result = ImportResult {
        success: false,
        assets_imported: 0,
        assets_updated: 0,
        errors: vec!["Nmap import not yet implemented".to_string()],
    };
    
    Ok(Json(result))
}

pub async fn import_csv(
    State(state): State<AppState>,
    body: String,
) -> Result<Json<ImportResult>, StatusCode> {
    // TODO: Implement CSV parsing
    let result = ImportResult {
        success: false,
        assets_imported: 0,
        assets_updated: 0,
        errors: vec!["CSV import not yet implemented".to_string()],
    };
    
    Ok(Json(result))
}

// Framework handlers
pub async fn get_frameworks(
    State(state): State<AppState>,
) -> Result<Json<Vec<Framework>>, StatusCode> {
    let frameworks = sqlx::query_as::<_, Framework>("SELECT * FROM frameworks ORDER BY name")
        .fetch_all(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(frameworks))
}

// Assessment handlers
pub async fn get_assessments(
    State(state): State<AppState>,
) -> Result<Json<Vec<Assessment>>, StatusCode> {
    let assessments = sqlx::query_as::<_, Assessment>("SELECT * FROM assessments ORDER BY created_at DESC")
        .fetch_all(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(assessments))
}

pub async fn create_assessment(
    State(state): State<AppState>,
    Json(payload): Json<CreateAssessmentRequest>,
) -> Result<Json<Assessment>, StatusCode> {
    let id = Uuid::new_v4().to_string();
    let now = Utc::now();

    let assessment = sqlx::query_as::<_, Assessment>(
        r#"
        INSERT INTO assessments (id, name, framework_id, description, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 'draft', ?, ?)
        RETURNING *
        "#
    )
    .bind(&id)
    .bind(&payload.name)
    .bind(&payload.framework_id)
    .bind(&payload.description)
    .bind(&now)
    .bind(&now)
    .fetch_one(&state.db)
    .await
    .map_err(|e| {
        tracing::error!("Failed to create assessment: {}", e);
        StatusCode::INTERNAL_SERVER_ERROR
    })?;

    Ok(Json(assessment))
}

// Dashboard handlers
use serde::Serialize;

#[derive(Serialize)]
pub struct DashboardStats {
    pub total_assets: i64,
    pub active_assets: i64,
    pub critical_assets: i64,
    pub total_assessments: i64,
    pub average_security_score: f64,
    pub critical_issues: i64,
}

#[derive(Serialize)]
pub struct RecentActivity {
    pub id: String,
    pub activity_type: String,
    pub message: String,
    pub timestamp: String,
    pub user: String,
}

pub async fn get_dashboard_stats(
    State(state): State<AppState>,
) -> Result<Json<DashboardStats>, StatusCode> {
    // Get total assets
    let total_assets: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assets")
        .fetch_one(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Get active assets
    let active_assets: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assets WHERE status = 'active'")
        .fetch_one(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Get critical assets
    let critical_assets: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assets WHERE criticality = 'critical'")
        .fetch_one(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Get total assessments
    let total_assessments: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assessments")
        .fetch_one(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Calculate average security score (placeholder - would be based on actual assessment scores)
    let average_security_score: f64 = if total_assessments > 0 {
        sqlx::query_scalar("SELECT AVG(CAST(score AS REAL)) FROM assessments")
            .fetch_one(&state.db)
            .await
            .unwrap_or(0.0)
    } else {
        0.0
    };

    // Count critical issues (placeholder - would be based on actual vulnerability data)
    let critical_issues = critical_assets;

    let stats = DashboardStats {
        total_assets,
        active_assets,
        critical_assets,
        total_assessments,
        average_security_score,
        critical_issues,
    };

    Ok(Json(stats))
}

pub async fn get_recent_activity(
    State(state): State<AppState>,
) -> Result<Json<Vec<RecentActivity>>, StatusCode> {
    // For now, return a simple activity log based on recent asset changes
    let activities = sqlx::query("SELECT id, name, created_at, updated_at FROM assets ORDER BY updated_at DESC LIMIT 10")
        .fetch_all(&state.db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let recent_activity: Vec<RecentActivity> = activities
        .into_iter()
        .map(|row| RecentActivity {
            id: Uuid::new_v4().to_string(),
            activity_type: "info".to_string(),
            message: format!("Asset '{}' was updated", row.get::<String, _>("name")),
            timestamp: row.get::<String, _>("updated_at"),
            user: "System".to_string(),
        })
        .collect();

    Ok(Json(recent_activity))
}
