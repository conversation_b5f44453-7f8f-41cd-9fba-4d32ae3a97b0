use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use sqlx::{sqlite::SqlitePool, Row};
use std::sync::Arc;
use tower_http::cors::CorsLayer;
use tracing::{info, warn};
use tracing_subscriber;

mod models;
mod handlers;
mod parsers;
mod frameworks;

use models::*;
use handlers::*;

#[derive(Clone)]
pub struct AppState {
    pub db: SqlitePool,
}

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    version: String,
}

async fn health_check() -> Json<HealthResponse> {
    Json(HealthResponse {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    })
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    // Database setup
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite:cybermap.db".to_string());

    let pool = SqlitePool::connect(&database_url).await?;

    // Run migrations
    sqlx::migrate!("./migrations").run(&pool).await?;

    let state = AppState { db: pool };

    // Build our application with routes
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/api/assets", get(get_assets).post(create_asset))
        .route("/api/assets/:id", get(get_asset).put(update_asset).delete(delete_asset))
        .route("/api/import/nmap", post(import_nmap))
        .route("/api/import/csv", post(import_csv))
        .route("/api/frameworks", get(get_frameworks))
        .route("/api/assessments", get(get_assessments).post(create_assessment))
        .route("/api/dashboard/stats", get(get_dashboard_stats))
        .route("/api/dashboard/activity", get(get_recent_activity))
        .layer(CorsLayer::permissive())
        .with_state(state);

    let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
    info!("CyberMap backend listening on http://0.0.0.0:8080");

    axum::serve(listener, app).await?;

    Ok(())
}
