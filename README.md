# 🛡️ CyberMap - Cybersecurity Infrastructure Mapper & Framework Alignment Tool

A local-first web application for IT and security teams to visually map infrastructure, document configurations, and assess cybersecurity posture against industry frameworks.

## 🎯 Features

- **Infrastructure Discovery & Mapping**: Import from nmap, Active Directory, cloud providers
- **Interactive Visualization**: Drag-and-drop canvas for infrastructure diagrams
- **Configuration Management**: Document security settings, policies, and technical details
- **Framework Assessment**: Map to NIST CSF, CIS Controls with gap analysis
- **Local-First**: Fully offline capable, air-gapped deployment ready
- **Export & Reporting**: PDF reports, CSV data, multiple diagram formats

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │    Storage      │
│                 │    │                 │    │                 │
│ React + TS      │◄──►│ Rust (Axum)     │◄──►│ SQLite          │
│ React Flow      │    │ Asset Parser    │    │ Local Files     │
│ Tailwind CSS    │    │ Framework APIs  │    │ JSON Exports    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Rust 1.70+
- Docker (optional)

### Quick Deployment

**Using the deployment script (recommended):**
```bash
./deploy.sh
```

This script automatically detects your environment and:
- Uses Docker if available (containerized deployment)
- Falls back to local development setup if Docker is not available
- Sets up all dependencies and starts both frontend and backend
- Provides health checks and error handling

### Manual Setup Options

#### Option 1: Docker Deployment (Recommended)
```bash
# Build and start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Option 2: Local Development
1. **Backend setup:**
```bash
cd backend
cargo build --release
mkdir -p data
RUST_LOG=info cargo run
```

2. **Frontend setup (in new terminal):**
```bash
cd frontend
npm install
npm run build
npx serve -s dist -l 3000
```

### Access Points
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8080
- **Health Check:** http://localhost:8080/health

## 📁 Project Structure

```
cybermap/
├── backend/                 # Rust backend
│   ├── src/
│   │   ├── main.rs         # Application entry point
│   │   ├── models/         # Data models
│   │   ├── handlers/       # API handlers
│   │   ├── parsers/        # Import parsers (nmap, CSV)
│   │   └── frameworks/     # NIST CSF, CIS Controls
│   ├── migrations/         # Database migrations
│   └── Cargo.toml
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Application pages
│   │   ├── hooks/          # Custom React hooks
│   │   ├── types/          # TypeScript definitions
│   │   └── utils/          # Utility functions
│   ├── public/
│   └── package.json
├── docs/                   # Documentation
├── docker-compose.yml      # Container orchestration
└── README.md
```

## 🔧 Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| Frontend | React 18 + TypeScript | Modern UI framework |
| Visualization | React Flow | Interactive diagrams |
| Styling | Tailwind CSS | Utility-first CSS |
| Backend | Rust + Axum | High-performance API |
| Database | SQLite | Local-first storage |
| Containerization | Docker | Deployment packaging |

## 📊 Supported Frameworks

- **NIST Cybersecurity Framework (CSF)**: Identify, Protect, Detect, Respond, Recover
- **CIS Controls**: 18 critical security controls
- **Custom Frameworks**: Extensible framework definition system

## 🔒 Security & Privacy

- **Local-First**: No cloud dependencies, fully air-gapped capable
- **Data Sovereignty**: All data stays on your infrastructure
- **Encrypted Storage**: Optional database encryption
- **Audit Logging**: Track all changes and access

## 📈 Roadmap

- [ ] Core infrastructure mapping
- [ ] NIST CSF integration
- [ ] CIS Controls mapping
- [ ] Advanced reporting
- [ ] MITRE ATT&CK integration
- [ ] Multi-user support
- [ ] API integrations

## 🤝 Contributing

See [CONTRIBUTING.md](docs/CONTRIBUTING.md) for development guidelines.

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.
